use std::time::{Duration, Instant};
use std::sync::Arc;
use tokio::sync::Mutex;
use std::collections::HashMap;

#[derive(Debug, Clone)]
pub struct QueryMetrics {
    pub total_queries: u64,
    pub total_duration: Duration,
    pub avg_duration: Duration,
    pub min_duration: Duration,
    pub max_duration: Duration,
}

impl QueryMetrics {
    pub fn new() -> Self {
        Self {
            total_queries: 0,
            total_duration: Duration::ZERO,
            avg_duration: Duration::ZERO,
            min_duration: Duration::MAX,
            max_duration: Duration::ZERO,
        }
    }

    pub fn add_measurement(&mut self, duration: Duration) {
        self.total_queries += 1;
        self.total_duration += duration;
        self.avg_duration = self.total_duration / self.total_queries as u32;
        
        if duration < self.min_duration {
            self.min_duration = duration;
        }
        if duration > self.max_duration {
            self.max_duration = duration;
        }
    }
}

#[derive(Debug, Clone)]
pub struct PerformanceMonitor {
    metrics: Arc<Mutex<HashMap<String, QueryMetrics>>>,
}

impl PerformanceMonitor {
    pub fn new() -> Self {
        Self {
            metrics: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    pub async fn record_query(&self, query_type: &str, duration: Duration) {
        let mut metrics = self.metrics.lock().await;
        let query_metrics = metrics.entry(query_type.to_string()).or_insert_with(QueryMetrics::new);
        query_metrics.add_measurement(duration);
    }

    pub async fn get_metrics(&self) -> HashMap<String, QueryMetrics> {
        self.metrics.lock().await.clone()
    }

    pub async fn print_summary(&self) {
        let metrics = self.metrics.lock().await;
        println!("\n=== Database Performance Summary ===");
        
        for (query_type, metrics) in metrics.iter() {
            println!("Query Type: {}", query_type);
            println!("  Total Queries: {}", metrics.total_queries);
            println!("  Average Duration: {:?}", metrics.avg_duration);
            println!("  Min Duration: {:?}", metrics.min_duration);
            println!("  Max Duration: {:?}", metrics.max_duration);
            println!("  Total Duration: {:?}", metrics.total_duration);
            println!();
        }
    }
}

pub struct QueryTimer {
    start_time: Instant,
    query_type: String,
    monitor: PerformanceMonitor,
}

impl QueryTimer {
    pub fn new(query_type: String, monitor: PerformanceMonitor) -> Self {
        Self {
            start_time: Instant::now(),
            query_type,
            monitor,
        }
    }

    pub async fn finish(self) {
        let duration = self.start_time.elapsed();
        self.monitor.record_query(&self.query_type, duration).await;
    }
}

// Macro for easy timing
#[macro_export]
macro_rules! time_query {
    ($monitor:expr, $query_type:expr, $block:block) => {{
        let timer = QueryTimer::new($query_type.to_string(), $monitor.clone());
        let result = $block;
        timer.finish().await;
        result
    }};
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;

    #[tokio::test]
    async fn test_performance_monitor() {
        let monitor = PerformanceMonitor::new();
        
        // Record some test measurements
        monitor.record_query("SELECT", Duration::from_millis(10)).await;
        monitor.record_query("SELECT", Duration::from_millis(20)).await;
        monitor.record_query("INSERT", Duration::from_millis(5)).await;
        
        let metrics = monitor.get_metrics().await;
        
        assert_eq!(metrics.len(), 2);
        assert!(metrics.contains_key("SELECT"));
        assert!(metrics.contains_key("INSERT"));
        
        let select_metrics = &metrics["SELECT"];
        assert_eq!(select_metrics.total_queries, 2);
        assert_eq!(select_metrics.avg_duration, Duration::from_millis(15));
    }
}