use axum::{
    http::StatusCode, 
    response::{IntoResponse, Response}
};

use tokio_postgres::<PERSON>rro<PERSON> as PostgresError;
use tokio_pg_mapper::Error as PostgresMapperError;
use serde::Serialize;
use thiserror::Error;
use validator::ValidationErrors;





#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct HttpResErrorResponse {
  msg: String,
}

#[derive(Error, Debug)]
pub enum HttpResError {
    #[error("Bad Request: {0:?}")]
    SerdeJSON(#[from] serde_json::Error),

    #[error("Bad Request: {0}")]
    BadRequest(String),

    #[error("Validation failed: {0:?}")]
    HttpValidation(#[from] ValidationErrors),

    #[error("A database operation failed: {0:?}")]
    Postgres(#[from] PostgresError),

    #[error("Failed to map database row: {0:?}")]
    PostgresMapper(#[from] PostgresMapperError),
}





impl IntoResponse for HttpResError {
    fn into_response(self) -> Response {
        let msg = self.to_string();
        let res = match &self {
            HttpResError::SerdeJSON(_) => {
                tracing::error!("JSON serialization error: {}", msg);
                (StatusCode::INTERNAL_SERVER_ERROR, msg)
            }

            HttpResError::BadRequest(_) => {
                tracing::warn!("Bad request: {}", msg);
                (StatusCode::BAD_REQUEST, msg)
            }

            HttpResError::HttpValidation(_) => {
                tracing::warn!("Validation error: {}", msg);
                (StatusCode::BAD_REQUEST, msg)
            }

            HttpResError::Postgres(pg_error) => {
                // Map PostgreSQL-specific errors to appropriate HTTP status codes
                let status = match pg_error.code() {
                    Some(code) => match code.code() {
                        // Constraint violations
                        "23505" => StatusCode::CONFLICT, // unique_violation
                        "23503" => StatusCode::BAD_REQUEST, // foreign_key_violation
                        "23502" => StatusCode::BAD_REQUEST, // not_null_violation
                        "23514" => StatusCode::BAD_REQUEST, // check_violation
                        
                        // Connection errors
                        "08000" | "08003" | "08006" => StatusCode::SERVICE_UNAVAILABLE,
                        
                        // Syntax errors
                        "42601" | "42703" | "42883" => StatusCode::INTERNAL_SERVER_ERROR,
                        
                        // Default for other database errors
                        _ => StatusCode::INTERNAL_SERVER_ERROR,
                    },
                    None => StatusCode::INTERNAL_SERVER_ERROR,
                };
                
                tracing::error!("PostgreSQL error [{}]: {}", 
                    pg_error.code().map(|c| c.code()).unwrap_or("unknown"), 
                    msg
                );
                (status, msg)
            }

            HttpResError::PostgresMapper(_) => {
                tracing::error!("PostgreSQL row mapping error: {}", msg);
                (StatusCode::INTERNAL_SERVER_ERROR, msg)
            }
        };
        
        res.into_response()
    }
}

