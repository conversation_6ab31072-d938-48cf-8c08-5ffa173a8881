
use std::env;
use tokio_postgres::{Client, NoTls, Error as PostgresError};

#[derive(Debug, <PERSON>lone)]
pub struct PostgresConfig {
    pub host: String,
    pub port: u16,
    pub dbname: String,
    pub user: String,
    pub password: String,
}

impl PostgresConfig {
    pub fn from_env() -> Result<Self, env::VarError> {
        Ok(PostgresConfig {
            host: env::var("POSTGRES_HOST")?,
            port: env::var("POSTGRES_PORT")?.parse().map_err(|_| env::VarError::NotPresent)?,
            dbname: env::var("POSTGRES_DB")?,
            user: env::var("POSTGRES_USER")?,
            password: env::var("POSTGRES_PASSWORD")?,
        })
    }

    pub fn connection_string(&self) -> String {
        format!(
            "host={} port={} dbname={} user={} password={}",
            self.host, self.port, self.dbname, self.user, self.password
        )
    }

    pub fn validate(&self) -> Result<(), String> {
        if self.host.is_empty() {
            return Err("PostgreSQL host cannot be empty".to_string());
        }
        if self.port == 0 {
            return Err("PostgreSQL port must be greater than 0".to_string());
        }
        if self.dbname.is_empty() {
            return Err("PostgreSQL database name cannot be empty".to_string());
        }
        if self.user.is_empty() {
            return Err("PostgreSQL user cannot be empty".to_string());
        }
        if self.password.is_empty() {
            return Err("PostgreSQL password cannot be empty".to_string());
        }
        Ok(())
    }
}


pub async fn pg_conn(config: &PostgresConfig) -> Result<Client, PostgresError> {
    // Validate configuration
    if let Err(e) = config.validate() {
        eprintln!("PostgreSQL configuration error: {}", e);
        // Create a dummy connection to get a proper error type
        match tokio_postgres::connect("invalid", tokio_postgres::NoTls).await {
            Ok(_) => unreachable!(),
            Err(err) => return Err(err),
        }
    }

    // Create connection string
    let connection_string = config.connection_string();

    // Connect to PostgreSQL
    let (client, connection) = tokio_postgres::connect(&connection_string, NoTls).await?;

    // Spawn the connection task
    tokio::spawn(async move {
        if let Err(e) = connection.await {
            eprintln!("PostgreSQL connection error: {}", e);
        }
    });

    // Test the connection with a simple query
    match client.simple_query("SELECT 1").await {
        Ok(_) => {
            println!("PostgreSQL connection established successfully");
        }
        Err(e) => {
            eprintln!("PostgreSQL connection test failed: {}", e);
            return Err(e);
        }
    }

    Ok(client)
}


pub async fn init_database_schema(client: &Client) -> Result<(), PostgresError> {
    // Create users table if it doesn't exist
    let create_table_sql = "
        CREATE TABLE IF NOT EXISTS users (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            age INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT NULL,
            deleted_at TIMESTAMP DEFAULT NULL
        );
    ";
    
    client.execute(create_table_sql, &[]).await?;
    
    // Create index if it doesn't exist
    let create_index_sql = "
        CREATE INDEX IF NOT EXISTS idx_users_deleted_at_age_name_id 
        ON users (deleted_at, age, name, id);
    ";
    
    client.execute(create_index_sql, &[]).await?;
    
    println!("Database schema initialized successfully");
    Ok(())
}
