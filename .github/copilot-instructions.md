# Copilot Instructions for rust-axum-server

## Project Overview
- This is a Rust web server using [Axum](https://github.com/tokio-rs/axum) as the main web framework.
- The project is organized by domain: `api/` (HTTP endpoints), `svr/` (core services), `biz/` (business logic), and `static/` (static files).
- Data persistence uses SQLite via `rusqlite` and custom wrappers. Redis is used for caching and message queueing.
- The main entry point is `main.rs`, which wires up the Axum server, state, and routes.

## Key Architectural Patterns
- **Routing**: All HTTP routes are defined in `api/`, grouped by domain (e.g., `dmail`, `test1`, `test2`). Each submodule exposes a `build_*_routes()` function returning an Axum `Router`.
- **State Management**: Shared state (DB pools, Redis, etc.) is managed via the `AppState` struct, passed to handlers using Axum's `State` extractor.
- **Database Access**: Use `state.dmail_db.call(|conn| { ... })` for SQLite access. Query patterns use `rusqlite` and custom `FromRow`/`Table` abstractions.
- **Error Handling**: Custom error types (e.g., `HttpResError` in `svr/error.rs`) are used for consistent API responses.
- **Middleware**: Custom middleware lives in `svr/middleware/` (e.g., `auth.rs`, `access_log.rs`). Register middleware in the main server setup.
- **Async**: All HTTP handlers are `async fn`, using `tokio` runtime.

## Developer Workflows
- **Build**: Use `cargo build` to compile. For release: `cargo build --release`.
- **Run**: Start the server with `cargo run`. The default port and config are set in `main.rs`.
- **Test**: Run tests with `cargo test`. Some integration tests may be in `test.rs` or under `api/test*/`.
- **Benchmarks**: Run `cargo bench` for benchmarks in `benches/`.
- **Logs**: Runtime logs are output to the `logs/` directory and may also appear in the console.

## Project-Specific Conventions
- **Module Naming**: Use short, lowercase names for modules and files. Route builders are named `build_*_routes`.
- **API Structure**: All API endpoints are nested under `/dmail`, `/test1`, `/test2`, etc., as defined in `api/`.
- **Database**: The SQLite DB file is `dmail.db` in the project root. Schema and migrations are managed manually (see `schema.rs`).
- **Error Types**: Always return custom error types for API handlers.
- **Validation**: Use `axum_valid::Valid` for request validation.

## Integration Points
- **Redis**: Used for caching and MQ, see `svr/redis.rs` and `svr/mq.rs`.
- **Email**: Email sending logic is in `svr/email.rs`.
- **Static Files**: Serve static files from `static/` (e.g., `404.html`).

## Examples
- See `api/dmail/user.rs` for a typical API handler pattern: extracting state, validating input, DB access, and returning JSON.
- See `svr/middleware/` for custom middleware patterns.

## References
- Main entry: `main.rs`
- Routing: `api/`, especially `dmail/mod.rs` and `dmail/user.rs`
- State & config: `svr/global.rs`, `AppState`
- Error handling: `svr/error.rs`
- DB: `svr/db.rs`, `schema.rs`, `dmail.db`
- Middleware: `svr/middleware/`
- Benchmarks: `benches/`

---
For questions or unclear patterns, review the referenced files or ask for clarification.
