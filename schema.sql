-- PostgreSQL Database Schema for Dmail Application
-- Migration from SQLite to PostgreSQL

-- Drop existing table if it exists
DROP TABLE IF EXISTS users CASCADE;

-- Create users table with PostgreSQL syntax
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    age INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT NULL,
    deleted_at TIMESTAMP DEFAULT NULL
);

-- Create index equivalent to SQLite index
CREATE INDEX idx_users_deleted_at_age_name_id ON users (deleted_at, age, name, id);

-- Add comments for documentation
COMMENT ON TABLE users IS 'User table migrated from SQLite';
COMMENT ON COLUMN users.id IS 'Primary key, auto-incrementing';
COMMENT ON COLUMN users.name IS 'User name, case-sensitive in PostgreSQL';
COMMENT ON COLUMN users.age IS 'User age in years';
COMMENT ON COLUMN users.created_at IS 'Record creation timestamp';
COMMENT ON COLUMN users.updated_at IS 'Record last update timestamp';
COMMENT ON COLUMN users.deleted_at IS 'Soft delete timestamp';