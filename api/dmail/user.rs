
use axum::{extract::{Query, State}, response::IntoResponse, routing::{get, post}, Json, Router};
use tokio_postgres::types::ToSql;

use crate::{biz::{PaginatorWith, Table, UserPayload}, svr::error::HttpResError, AppState};

pub fn build_user_routes() -> Router<AppState>{
    Router::new().nest("/user",Router::new()
        // query & create & for biz

        // query
        .route("/q", get(q))
        // create
        .route("/c", post(c))
        // .route("/cBulk", post(cBulk))
        // // update
        // .route("/u", post(u))
        // .route("/uBulk", post(uBulk))
        // // delete
        // .route("/d/{:id}", get(d))
        // .route("/dBulk", post(dBulk))

        // // real_delete
        // .route("/dReal/{:id}", get(dReal))
        // .route("/dRealBulk", post(dRealBulk))


    )
}
// info!("{:?}",  &new_user); // 异步高效输出

// ==================================================


async fn q(State(state): State<AppState>, Query(payload): Query<PaginatorWith<UserPayload>>) -> Result<impl IntoResponse, HttpResError> {
    // Use prepared statements for better performance
    let (sql, params) = build_optimized_query(&payload);
    
    // Convert to references for query
    let param_refs: Vec<&(dyn ToSql + Sync)> = params.iter().map(|p| &**p as &(dyn ToSql + Sync)).collect();

    // Use prepared statement for better performance
    let stmt = state.dmail_db.batch_execute(query)// .prepare(&sql).await?;
    let rows = state.dmail_db.query(&stmt, &param_refs).await?;
    
    // Map results using manual mapping with capacity pre-allocation
    let mut users = Vec::with_capacity(rows.len());
    for row in rows {
        let user = Table::<UserPayload>::from_row(&row)?;
        users.push(user);
    }

    Ok(Json(users))
}

fn build_optimized_query(payload: &PaginatorWith<UserPayload>) -> (String, Vec<Box<dyn ToSql + Send + Sync>>) {
    let mut sql = String::from("SELECT id, name, age, created_at, updated_at, deleted_at FROM users WHERE deleted_at IS NULL");
    let mut params: Vec<Box<dyn ToSql + Send + Sync>> = Vec::new();
    let mut param_count = 0;

    // Build dynamic WHERE clause with PostgreSQL parameter syntax
    if let Some(age) = payload.custom.age {
        param_count += 1;
        sql.push_str(&format!(" AND age = ${}", param_count));
        params.push(Box::new(age));
    }
    
    if let Some(name) = payload.custom.name.clone() {
        param_count += 1;
        sql.push_str(&format!(" AND name = ${}", param_count));
        params.push(Box::new(name));
    }

    // Add ORDER BY for consistent pagination
    sql.push_str(" ORDER BY id");

    // Handle pagination - cursor-based is more efficient for large datasets
    if let Some(id) = payload.id {
        param_count += 1;
        sql.push_str(&format!(" AND id > ${}", param_count));
        params.push(Box::new(id));
        
        param_count += 1;
        sql.push_str(&format!(" LIMIT ${}", param_count));
        params.push(Box::new(payload.ps.unwrap_or(10) as i64));
    } else {
        param_count += 1;
        sql.push_str(&format!(" LIMIT ${}", param_count));
        params.push(Box::new(payload.ps.unwrap_or(10) as i64));
        
        param_count += 1;
        let offset = payload.pn.unwrap_or(0) as i64 * payload.ps.unwrap_or(10) as i64;
        sql.push_str(&format!(" OFFSET ${}", param_count));
        params.push(Box::new(offset));
    }

    (sql, params)
}


async fn c(State(state): State<AppState>, Json(payload): Json<UserPayload>) -> Result<impl IntoResponse, HttpResError> {
    let sql = "INSERT INTO users (name, age) VALUES ($1, $2) RETURNING id, name, age, created_at, updated_at, deleted_at";
    
    // Use prepared statement
    let stmt = state.dmail_db.prepare(sql).await?;
    let row = state.dmail_db.query_one(&stmt, &[&payload.name, &payload.age]).await?;
    
    let new_user = Table::<UserPayload>::from_row(&row)?;
    
    Ok(Json(new_user))
}



// async fn cBulk(State(state): State<AppState>,Valid(Json(payload)): Valid<Json<Vec<UserPayload>>>) -> Result<impl IntoResponse, HttpResError> {
//     if payload.is_empty() { return Err(HttpResError::BadRequest("empty payload".to_owned()));}
//     let new_users = state.dmail_db.get().await?.interact(move |conn|  {
//         let values_placeholders = payload.iter().map(|_| "(?,?)").collect::<Vec<&str>>().join(",");
//         let sql = format!("INSERT INTO user (name, age) VALUES {} RETURNING id, name, age, created_at",values_placeholders);
//         let params_list: Vec<Value> = payload.into_iter().flat_map(|user_data| [user_data.name.into(),user_data.age.into()]).collect();
//         conn.prepare_cached(&sql)?.query_map(params_from_iter(params_list), Table::<UserPayload>::try_from_row)?.collect::<Result<Vec<Table<UserPayload>>, Error>>()
//     }).await??;
//     Ok(Json(new_users))
// }


// // #[debug_handler] // 需要单独的添加 cargo add axum-macros
// async fn u(State(state): State<AppState>,Valid(Json(payload)): Valid<Json<Table<UserPayload>>>) -> Result<impl IntoResponse, HttpResError> {
//     state.dmail_db.get().await?.interact(move |conn|  {
//         conn.prepare_cached("UPDATE user SET name = :name, age = :age,updated_at = CURRENT_TIMESTAMP WHERE id = :id")?.execute(named_params!{
//             ":id": &payload.id,
//             ":name": &payload.custom.name,
//             ":age": &payload.custom.age,
//         })
//     }).await??;

//     Ok(())
// }


// async fn uBulk(State(state): State<AppState>,Valid(Json(payload)): Valid<Json<Vec<Table<UserPayload>>>>) -> Result<impl IntoResponse, HttpResError> {
//     if payload.is_empty() {return Err(HttpResError::BadRequest("empty payload".to_owned()));}
//     state.dmail_db.get().await?.interact(move |conn|  {
//         let sql = format!("
//             INSERT INTO user (id, name, age) VALUES {} ON CONFLICT(id) DO UPDATE SET updated_at = CURRENT_TIMESTAMP, \
//             name = excluded.name,\
//             age = excluded.age;",
//             payload.iter().map(|_| "(?,?,?)").collect::<Vec<_>>().join(",")
//         );
//         let params_list: Vec<Value> = payload.into_iter().flat_map(|item_data| {[
//             item_data.id.into(),
//             item_data.custom.name.into(),
//             item_data.custom.age.into(),
//         ]}).collect();
//         conn.prepare_cached(&sql)?.execute(params_from_iter(params_list))
//     }).await??;
//     Ok(())
// }



// async fn d(State(state): State<AppState>,Path(id): Path<i64>,) -> Result<impl IntoResponse, HttpResError> {
//     state.dmail_db.get().await?.interact(move |conn| {
//         conn.prepare_cached("UPDATE user SET deleted_at = CURRENT_TIMESTAMP WHERE id = :id")?.execute(named_params!{":id": id})
//     }).await??;
//     Ok(())
// }


// async fn dBulk(State(state): State<AppState>,Valid(Json(payload)): Valid<Json<Vec<Table<()>>>>) -> Result<impl IntoResponse, HttpResError> {
//     if payload.is_empty() {return Err(HttpResError::BadRequest("empty payload".to_owned()));}
//     state.dmail_db.get().await?.interact(move |conn|  {
//         let sql = format!("UPDATE user SET deleted_at = CURRENT_TIMESTAMP WHERE id IN ({})",payload.iter().map(|_| "?").collect::<Vec<_>>().join(","));
//         let params_list: Vec<Value> = payload.iter().map(|user| user.id.into()).collect();
//         conn.prepare_cached(&sql)?.execute(params_from_iter(params_list))
//     }).await??;
//     Ok(())
// }






// async fn dReal(State(state): State<AppState>,Path(id): Path<i64>,) -> Result<impl IntoResponse, HttpResError> {
//     state.dmail_db.get().await?.interact(move |conn| {
//         conn.prepare_cached("DELETE FROM user WHERE id = :id")?.execute(named_params!{":id": id})
//     }).await??;

//     Ok(())
// }

// async fn dRealBulk(State(state): State<AppState>,Json(payload): Json<Vec<Table<()>>>) -> Result<impl IntoResponse, HttpResError> {
//     if payload.is_empty() {return Err(HttpResError::BadRequest("empty payload".to_owned()));}
//     state.dmail_db.get().await?.interact(move |conn| {
//         let sql = format!("DELETE FROM user WHERE id IN ({})", payload.iter().map(|_| "?").collect::<Vec<&str>>().join(","));
//         let params_list: Vec<Value> = payload.iter().map(|user| user.id.into()).collect();
//         conn.prepare_cached(&sql)?.execute(params_from_iter(params_list))
//     }).await??;
//     Ok(())
// }








