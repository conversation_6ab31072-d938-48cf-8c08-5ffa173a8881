use std::process::Command;
use std::time::Duration;
use tokio::time::sleep;
use serde_json::{json, Value};

const BASE_URL: &str = "http://localhost:8089";

#[tokio::test]
#[ignore] // This test requires the server to be running
async fn test_complete_user_workflow() {
    // Wait for server to be ready
    sleep(Duration::from_secs(2)).await;

    // Test 1: Create a new user
    let create_response = create_user("E2E Test User", 30).await;
    assert!(create_response.is_ok(), "Failed to create user: {:?}", create_response.err());
    
    let created_user = create_response.unwrap();
    let user_id = created_user["id"].as_i64().expect("User should have an ID");
    
    assert_eq!(created_user["name"], "E2E Test User");
    assert_eq!(created_user["age"], 30);
    assert!(created_user["created_at"].is_string());

    // Test 2: Query all users
    let query_response = query_users(None, None, Some(10), Some(0)).await;
    assert!(query_response.is_ok(), "Failed to query users: {:?}", query_response.err());
    
    let users = query_response.unwrap();
    assert!(users.as_array().unwrap().len() > 0, "Should have at least one user");

    // Test 3: Query users with age filter
    let age_filter_response = query_users(Some(30), None, Some(10), Some(0)).await;
    assert!(age_filter_response.is_ok(), "Failed to query users with age filter: {:?}", age_filter_response.err());
    
    let filtered_users = age_filter_response.unwrap();
    let filtered_array = filtered_users.as_array().unwrap();
    
    // Verify all returned users have age 30
    for user in filtered_array {
        assert_eq!(user["age"], 30, "All filtered users should have age 30");
    }

    // Test 4: Query users with name filter
    let name_filter_response = query_users(None, Some("E2E Test User"), Some(10), Some(0)).await;
    assert!(name_filter_response.is_ok(), "Failed to query users with name filter: {:?}", name_filter_response.err());
    
    let name_filtered_users = name_filter_response.unwrap();
    let name_filtered_array = name_filtered_users.as_array().unwrap();
    
    assert!(name_filtered_array.len() >= 1, "Should find at least our test user");
    let found_user = name_filtered_array.iter().find(|u| u["id"] == user_id);
    assert!(found_user.is_some(), "Should find our created user");

    // Test 5: Test pagination
    let page1_response = query_users(None, None, Some(5), Some(0)).await;
    assert!(page1_response.is_ok(), "Failed to get page 1: {:?}", page1_response.err());
    
    let page2_response = query_users(None, None, Some(5), Some(1)).await;
    assert!(page2_response.is_ok(), "Failed to get page 2: {:?}", page2_response.err());
    
    let page1_users = page1_response.unwrap().as_array().unwrap().clone();
    let page2_users = page2_response.unwrap().as_array().unwrap().clone();
    
    // Verify pagination works (pages should have different users if there are enough users)
    if page1_users.len() == 5 && page2_users.len() > 0 {
        let page1_ids: Vec<i64> = page1_users.iter().map(|u| u["id"].as_i64().unwrap()).collect();
        let page2_ids: Vec<i64> = page2_users.iter().map(|u| u["id"].as_i64().unwrap()).collect();
        
        // Pages should not have overlapping users
        for id in page2_ids {
            assert!(!page1_ids.contains(&id), "Pages should not have overlapping users");
        }
    }

    println!("✅ All end-to-end tests passed!");
}

#[tokio::test]
#[ignore] // This test requires the server to be running
async fn test_concurrent_operations() {
    // Wait for server to be ready
    sleep(Duration::from_secs(2)).await;

    // Create multiple users concurrently
    let create_futures = (1..=10).map(|i| {
        create_user(&format!("Concurrent User {}", i), 20 + i)
    });

    let create_results = futures::future::join_all(create_futures).await;
    
    // Verify all creations succeeded
    for (i, result) in create_results.iter().enumerate() {
        assert!(result.is_ok(), "Failed to create concurrent user {}: {:?}", i + 1, result.as_ref().err());
    }

    // Query users concurrently
    let query_futures = (0..5).map(|_| {
        query_users(None, None, Some(20), Some(0))
    });

    let query_results = futures::future::join_all(query_futures).await;
    
    // Verify all queries succeeded
    for (i, result) in query_results.iter().enumerate() {
        assert!(result.is_ok(), "Failed concurrent query {}: {:?}", i + 1, result.as_ref().err());
    }

    println!("✅ Concurrent operations test passed!");
}

#[tokio::test]
#[ignore] // This test requires the server to be running
async fn test_error_handling() {
    // Wait for server to be ready
    sleep(Duration::from_secs(2)).await;

    // Test invalid JSON
    let client = reqwest::Client::new();
    let invalid_json_response = client
        .post(&format!("{}/dmail/user/c", BASE_URL))
        .header("content-type", "application/json")
        .body("invalid json")
        .send()
        .await;

    assert!(invalid_json_response.is_ok());
    let response = invalid_json_response.unwrap();
    assert_eq!(response.status(), 400, "Should return 400 for invalid JSON");

    // Test empty request body
    let empty_body_response = client
        .post(&format!("{}/dmail/user/c", BASE_URL))
        .header("content-type", "application/json")
        .body("{}")
        .send()
        .await;

    assert!(empty_body_response.is_ok());
    let response = empty_body_response.unwrap();
    // Should either succeed with null values or fail with validation error
    assert!(response.status().is_success() || response.status().is_client_error());

    println!("✅ Error handling test passed!");
}

async fn create_user(name: &str, age: i32) -> Result<Value, Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    let user_data = json!({
        "name": name,
        "age": age
    });

    let response = client
        .post(&format!("{}/dmail/user/c", BASE_URL))
        .header("content-type", "application/json")
        .json(&user_data)
        .send()
        .await?;

    if !response.status().is_success() {
        return Err(format!("HTTP error: {}", response.status()).into());
    }

    let user: Value = response.json().await?;
    Ok(user)
}

async fn query_users(
    age: Option<i32>,
    name: Option<&str>,
    ps: Option<i32>,
    pn: Option<i32>,
) -> Result<Value, Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    let mut url = format!("{}/dmail/user/q", BASE_URL);
    let mut params = Vec::new();

    if let Some(age) = age {
        params.push(format!("age={}", age));
    }
    if let Some(name) = name {
        params.push(format!("name={}", urlencoding::encode(name)));
    }
    if let Some(ps) = ps {
        params.push(format!("ps={}", ps));
    }
    if let Some(pn) = pn {
        params.push(format!("pn={}", pn));
    }

    if !params.is_empty() {
        url.push('?');
        url.push_str(&params.join("&"));
    }

    let response = client.get(&url).send().await?;

    if !response.status().is_success() {
        return Err(format!("HTTP error: {}", response.status()).into());
    }

    let users: Value = response.json().await?;
    Ok(users)
}

// Helper function to start the server for testing
pub fn start_test_server() -> std::process::Child {
    Command::new("cargo")
        .args(&["run", "--bin", "main"])
        .spawn()
        .expect("Failed to start test server")
}

#[cfg(test)]
mod integration_setup {
    use super::*;
    use std::sync::Once;
    use std::process::Child;
    use std::sync::Mutex;

    static INIT: Once = Once::new();
    static mut SERVER_PROCESS: Option<Mutex<Child>> = None;

    pub fn setup_test_server() {
        INIT.call_once(|| {
            let server = start_test_server();
            unsafe {
                SERVER_PROCESS = Some(Mutex::new(server));
            }
            
            // Give the server time to start
            std::thread::sleep(Duration::from_secs(3));
        });
    }
}