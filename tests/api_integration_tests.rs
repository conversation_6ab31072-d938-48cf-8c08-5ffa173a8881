use axum::{
    body::Body,
    http::{Request, StatusCode},
    Router,
};
use serde_json::{json, Value};
use std::{env, sync::Arc};
use tower::ServiceExt;

use server::{
    api::dmail::build_dmail_routes,
    biz::{Table, UserPayload},
    svr::db::{pg_conn, init_database_schema, PostgresConfig},
    AppState,
};

async fn create_test_app() -> Router {
    // Set up test database connection
    let config = PostgresConfig {
        host: "localhost".to_string(),
        port: 5432,
        dbname: "dmail".to_string(),
        user: "postgres".to_string(),
        password: "123456".to_string(),
    };

    let client = pg_conn(&config).await.expect("Failed to connect to test database");
    init_database_schema(&client).await.expect("Failed to initialize schema");

    let app_state = AppState {
        dmail_db: Arc::new(client),
    };

    Router::new()
        .merge(build_dmail_routes())
        .with_state(app_state)
}

#[tokio::test]
#[ignore] // This test requires a running PostgreSQL instance
async fn test_user_creation_endpoint() {
    let app = create_test_app().await;

    let user_data = json!({
        "name": "Integration Test User",
        "age": 28
    });

    let request = Request::builder()
        .method("POST")
        .uri("/user/c")
        .header("content-type", "application/json")
        .body(Body::from(user_data.to_string()))
        .unwrap();

    let response = app.oneshot(request).await.unwrap();

    assert_eq!(response.status(), StatusCode::OK);

    let body = axum::body::to_bytes(response.into_body(), usize::MAX).await.unwrap();
    let response_json: Value = serde_json::from_slice(&body).unwrap();

    assert!(response_json["id"].as_i64().unwrap() > 0);
    assert_eq!(response_json["name"], "Integration Test User");
    assert_eq!(response_json["age"], 28);
    assert!(response_json["created_at"].is_string());

    // Clean up test data
    let cleanup_app = create_test_app().await;
    let cleanup_request = Request::builder()
        .method("GET")
        .uri("/user/q?name=Integration Test User")
        .body(Body::empty())
        .unwrap();

    let cleanup_response = cleanup_app.oneshot(cleanup_request).await.unwrap();
    let cleanup_body = axum::body::to_bytes(cleanup_response.into_body(), usize::MAX).await.unwrap();
    let users: Vec<Table<UserPayload>> = serde_json::from_slice(&cleanup_body).unwrap();

    if let Some(user) = users.first() {
        // In a real test, you'd want to add a delete endpoint for cleanup
        // For now, we'll leave the test data
    }
}

#[tokio::test]
#[ignore] // This test requires a running PostgreSQL instance
async fn test_user_query_endpoint() {
    let app = create_test_app().await;

    // First create a test user
    let user_data = json!({
        "name": "Query Test User",
        "age": 32
    });

    let create_request = Request::builder()
        .method("POST")
        .uri("/user/c")
        .header("content-type", "application/json")
        .body(Body::from(user_data.to_string()))
        .unwrap();

    let create_response = app.clone().oneshot(create_request).await.unwrap();
    assert_eq!(create_response.status(), StatusCode::OK);

    // Now query for users
    let query_request = Request::builder()
        .method("GET")
        .uri("/user/q?ps=10&pn=0")
        .body(Body::empty())
        .unwrap();

    let query_response = app.oneshot(query_request).await.unwrap();
    assert_eq!(query_response.status(), StatusCode::OK);

    let body = axum::body::to_bytes(query_response.into_body(), usize::MAX).await.unwrap();
    let users: Vec<Table<UserPayload>> = serde_json::from_slice(&body).unwrap();

    assert!(!users.is_empty());
    
    // Verify that our test user is in the results
    let test_user = users.iter().find(|u| u.custom.name == Some("Query Test User".to_string()));
    assert!(test_user.is_some());
    assert_eq!(test_user.unwrap().custom.age, Some(32));
}

#[tokio::test]
#[ignore] // This test requires a running PostgreSQL instance
async fn test_user_query_with_filters() {
    let app = create_test_app().await;

    // Create multiple test users
    let users_data = vec![
        json!({"name": "Filter Test User 1", "age": 25}),
        json!({"name": "Filter Test User 2", "age": 30}),
        json!({"name": "Filter Test User 3", "age": 35}),
    ];

    for user_data in users_data {
        let create_request = Request::builder()
            .method("POST")
            .uri("/user/c")
            .header("content-type", "application/json")
            .body(Body::from(user_data.to_string()))
            .unwrap();

        let create_response = app.clone().oneshot(create_request).await.unwrap();
        assert_eq!(create_response.status(), StatusCode::OK);
    }

    // Test filtering by age
    let age_filter_request = Request::builder()
        .method("GET")
        .uri("/user/q?age=30")
        .body(Body::empty())
        .unwrap();

    let age_filter_response = app.clone().oneshot(age_filter_request).await.unwrap();
    assert_eq!(age_filter_response.status(), StatusCode::OK);

    let body = axum::body::to_bytes(age_filter_response.into_body(), usize::MAX).await.unwrap();
    let filtered_users: Vec<Table<UserPayload>> = serde_json::from_slice(&body).unwrap();

    let age_30_users: Vec<_> = filtered_users.iter().filter(|u| u.custom.age == Some(30)).collect();
    assert!(!age_30_users.is_empty());

    // Test filtering by name
    let name_filter_request = Request::builder()
        .method("GET")
        .uri("/user/q?name=Filter%20Test%20User%201")
        .body(Body::empty())
        .unwrap();

    let name_filter_response = app.oneshot(name_filter_request).await.unwrap();
    assert_eq!(name_filter_response.status(), StatusCode::OK);

    let body = axum::body::to_bytes(name_filter_response.into_body(), usize::MAX).await.unwrap();
    let name_filtered_users: Vec<Table<UserPayload>> = serde_json::from_slice(&body).unwrap();

    assert_eq!(name_filtered_users.len(), 1);
    assert_eq!(name_filtered_users[0].custom.name, Some("Filter Test User 1".to_string()));
}

#[tokio::test]
#[ignore] // This test requires a running PostgreSQL instance
async fn test_pagination() {
    let app = create_test_app().await;

    // Create multiple test users for pagination testing
    for i in 1..=15 {
        let user_data = json!({
            "name": format!("Pagination Test User {}", i),
            "age": 20 + i
        });

        let create_request = Request::builder()
            .method("POST")
            .uri("/user/c")
            .header("content-type", "application/json")
            .body(Body::from(user_data.to_string()))
            .unwrap();

        let create_response = app.clone().oneshot(create_request).await.unwrap();
        assert_eq!(create_response.status(), StatusCode::OK);
    }

    // Test first page
    let page1_request = Request::builder()
        .method("GET")
        .uri("/user/q?ps=5&pn=0")
        .body(Body::empty())
        .unwrap();

    let page1_response = app.clone().oneshot(page1_request).await.unwrap();
    assert_eq!(page1_response.status(), StatusCode::OK);

    let body = axum::body::to_bytes(page1_response.into_body(), usize::MAX).await.unwrap();
    let page1_users: Vec<Table<UserPayload>> = serde_json::from_slice(&body).unwrap();

    assert!(page1_users.len() <= 5); // Should respect the page size limit

    // Test second page
    let page2_request = Request::builder()
        .method("GET")
        .uri("/user/q?ps=5&pn=1")
        .body(Body::empty())
        .unwrap();

    let page2_response = app.oneshot(page2_request).await.unwrap();
    assert_eq!(page2_response.status(), StatusCode::OK);

    let body = axum::body::to_bytes(page2_response.into_body(), usize::MAX).await.unwrap();
    let page2_users: Vec<Table<UserPayload>> = serde_json::from_slice(&body).unwrap();

    // Verify that page 2 has different users than page 1
    if !page1_users.is_empty() && !page2_users.is_empty() {
        let page1_ids: Vec<i64> = page1_users.iter().map(|u| u.id).collect();
        let page2_ids: Vec<i64> = page2_users.iter().map(|u| u.id).collect();
        
        // Pages should have different users
        assert!(page1_ids.iter().all(|id| !page2_ids.contains(id)));
    }
}

#[tokio::test]
#[ignore] // This test requires a running PostgreSQL instance
async fn test_error_handling() {
    let app = create_test_app().await;

    // Test invalid JSON
    let invalid_json_request = Request::builder()
        .method("POST")
        .uri("/user/c")
        .header("content-type", "application/json")
        .body(Body::from("invalid json"))
        .unwrap();

    let invalid_json_response = app.clone().oneshot(invalid_json_request).await.unwrap();
    assert_eq!(invalid_json_response.status(), StatusCode::BAD_REQUEST);

    // Test missing required fields (if validation is implemented)
    let missing_fields_request = Request::builder()
        .method("POST")
        .uri("/user/c")
        .header("content-type", "application/json")
        .body(Body::from("{}"))
        .unwrap();

    let missing_fields_response = app.oneshot(missing_fields_request).await.unwrap();
    // This should either succeed with null values or fail with validation error
    // depending on the validation rules
    assert!(missing_fields_response.status().is_success() || missing_fields_response.status().is_client_error());
}