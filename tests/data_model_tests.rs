use server::biz::{Table, UserPayload, FromRow};
use time::OffsetDateTime;
use tokio_postgres::{Client, NoTls, Row};

// Mock function to create a test row (this would normally come from a real database query)
async fn create_test_client() -> Client {
    let (client, connection) = tokio_postgres::connect(
        "host=localhost port=5432 dbname=dmail user=postgres password=123456",
        NoTls,
    ).await.expect("Failed to connect to test database");
    
    tokio::spawn(async move {
        if let Err(e) = connection.await {
            eprintln!("Connection error: {}", e);
        }
    });
    
    client
}

#[tokio::test]
#[ignore] // This test requires a running PostgreSQL instance
async fn test_user_payload_from_row() {
    let client = create_test_client().await;
    
    // Create a test user in the database
    let insert_sql = "INSERT INTO users (name, age) VALUES ($1, $2) RETURNING id, name, age, created_at, updated_at, deleted_at";
    let stmt = client.prepare(insert_sql).await.expect("Failed to prepare statement");
    let row = client.query_one(&stmt, &[&Some("Test User".to_string()), &Some(25i32)]).await.expect("Failed to insert test user");
    
    // Test UserPayload mapping
    let user_payload = UserPayload::from_row(&row).expect("Failed to map UserPayload from row");
    assert_eq!(user_payload.name, Some("Test User".to_string()));
    assert_eq!(user_payload.age, Some(25));
    
    // Clean up
    let cleanup_sql = "DELETE FROM users WHERE name = $1";
    let cleanup_stmt = client.prepare(cleanup_sql).await.expect("Failed to prepare cleanup statement");
    client.execute(&cleanup_stmt, &[&"Test User"]).await.expect("Failed to cleanup test data");
}

#[tokio::test]
#[ignore] // This test requires a running PostgreSQL instance
async fn test_table_from_row() {
    let client = create_test_client().await;
    
    // Create a test user in the database
    let insert_sql = "INSERT INTO users (name, age) VALUES ($1, $2) RETURNING id, name, age, created_at, updated_at, deleted_at";
    let stmt = client.prepare(insert_sql).await.expect("Failed to prepare statement");
    let row = client.query_one(&stmt, &[&Some("Table Test User".to_string()), &Some(30i32)]).await.expect("Failed to insert test user");
    
    // Test Table<UserPayload> mapping
    let table_user = Table::<UserPayload>::from_row(&row).expect("Failed to map Table from row");
    
    assert!(table_user.id > 0);
    assert_eq!(table_user.custom.name, Some("Table Test User".to_string()));
    assert_eq!(table_user.custom.age, Some(30));
    assert!(table_user.created_at.is_some());
    assert!(table_user.updated_at.is_none());
    assert!(table_user.deleted_at.is_none());
    
    // Clean up
    let cleanup_sql = "DELETE FROM users WHERE name = $1";
    let cleanup_stmt = client.prepare(cleanup_sql).await.expect("Failed to prepare cleanup statement");
    client.execute(&cleanup_stmt, &[&"Table Test User"]).await.expect("Failed to cleanup test data");
}

#[test]
fn test_table_helper_methods() {
    let user_payload = UserPayload {
        name: Some("Helper Test User".to_string()),
        age: Some(35),
    };
    
    let mut table_user = Table::new_with_timestamp(1, user_payload);
    
    // Test initial state
    assert_eq!(table_user.id, 1);
    assert_eq!(table_user.custom.name, Some("Helper Test User".to_string()));
    assert_eq!(table_user.custom.age, Some(35));
    assert!(table_user.created_at.is_some());
    assert!(table_user.updated_at.is_none());
    assert!(table_user.deleted_at.is_none());
    assert!(!table_user.is_deleted());
    
    // Test mark_updated
    table_user.mark_updated();
    assert!(table_user.updated_at.is_some());
    
    // Test mark_deleted
    table_user.mark_deleted();
    assert!(table_user.deleted_at.is_some());
    assert!(table_user.is_deleted());
}

#[test]
fn test_user_payload_serialization() {
    let user_payload = UserPayload {
        name: Some("Serialization Test".to_string()),
        age: Some(40),
    };
    
    // Test serialization to JSON
    let json = serde_json::to_string(&user_payload).expect("Failed to serialize UserPayload");
    assert!(json.contains("Serialization Test"));
    assert!(json.contains("40"));
    
    // Test deserialization from JSON
    let deserialized: UserPayload = serde_json::from_str(&json).expect("Failed to deserialize UserPayload");
    assert_eq!(deserialized.name, user_payload.name);
    assert_eq!(deserialized.age, user_payload.age);
}

#[test]
fn test_table_serialization() {
    let user_payload = UserPayload {
        name: Some("Table Serialization Test".to_string()),
        age: Some(45),
    };
    
    let table_user = Table::new_with_timestamp(2, user_payload);
    
    // Test serialization to JSON
    let json = serde_json::to_string(&table_user).expect("Failed to serialize Table");
    assert!(json.contains("Table Serialization Test"));
    assert!(json.contains("45"));
    assert!(json.contains("\"id\":2"));
    
    // Test deserialization from JSON
    let deserialized: Table<UserPayload> = serde_json::from_str(&json).expect("Failed to deserialize Table");
    assert_eq!(deserialized.id, table_user.id);
    assert_eq!(deserialized.custom.name, table_user.custom.name);
    assert_eq!(deserialized.custom.age, table_user.custom.age);
}

#[test]
fn test_timestamp_compatibility() {
    let now = OffsetDateTime::now_utc();
    let user_payload = UserPayload {
        name: Some("Timestamp Test".to_string()),
        age: Some(50),
    };
    
    let table_user = Table {
        id: 3,
        created_at: Some(now),
        updated_at: None,
        deleted_at: None,
        custom: user_payload,
    };
    
    // Verify timestamp is properly stored and accessible
    assert!(table_user.created_at.is_some());
    assert_eq!(table_user.created_at.unwrap(), now);
}