use std::env;
use server::svr::db::{pg_conn, init_database_schema, PostgresConfig};

#[tokio::test]
async fn test_postgres_config_from_env() {
    // Set test environment variables
    env::set_var("POSTGRES_HOST", "localhost");
    env::set_var("POSTGRES_PORT", "5432");
    env::set_var("POSTGRES_DB", "test_db");
    env::set_var("POSTGRES_USER", "test_user");
    env::set_var("POSTGRES_PASSWORD", "test_password");

    let config = PostgresConfig::from_env().expect("Failed to load config from env");
    
    assert_eq!(config.host, "localhost");
    assert_eq!(config.port, 5432);
    assert_eq!(config.dbname, "test_db");
    assert_eq!(config.user, "test_user");
    assert_eq!(config.password, "test_password");
}

#[tokio::test]
async fn test_postgres_config_validation() {
    let valid_config = PostgresConfig {
        host: "localhost".to_string(),
        port: 5432,
        dbname: "test_db".to_string(),
        user: "test_user".to_string(),
        password: "test_password".to_string(),
    };
    
    assert!(valid_config.validate().is_ok());

    let invalid_config = PostgresConfig {
        host: "".to_string(),
        port: 0,
        dbname: "".to_string(),
        user: "".to_string(),
        password: "".to_string(),
    };
    
    assert!(invalid_config.validate().is_err());
}

#[tokio::test]
async fn test_postgres_connection_string() {
    let config = PostgresConfig {
        host: "localhost".to_string(),
        port: 5432,
        dbname: "test_db".to_string(),
        user: "test_user".to_string(),
        password: "test_password".to_string(),
    };
    
    let connection_string = config.connection_string();
    assert_eq!(
        connection_string,
        "host=localhost port=5432 dbname=test_db user=test_user password=test_password"
    );
}

#[tokio::test]
#[ignore] // This test requires a running PostgreSQL instance
async fn test_postgres_connection_success() {
    let config = PostgresConfig {
        host: "localhost".to_string(),
        port: 5432,
        dbname: "dmail".to_string(),
        user: "postgres".to_string(),
        password: "123456".to_string(),
    };
    
    let client = pg_conn(&config).await;
    assert!(client.is_ok(), "Failed to connect to PostgreSQL: {:?}", client.err());
}

#[tokio::test]
async fn test_postgres_connection_failure() {
    let invalid_config = PostgresConfig {
        host: "nonexistent_host".to_string(),
        port: 9999,
        dbname: "nonexistent_db".to_string(),
        user: "nonexistent_user".to_string(),
        password: "wrong_password".to_string(),
    };
    
    let client = pg_conn(&invalid_config).await;
    assert!(client.is_err(), "Connection should have failed with invalid config");
}

#[tokio::test]
#[ignore] // This test requires a running PostgreSQL instance
async fn test_database_schema_initialization() {
    let config = PostgresConfig {
        host: "localhost".to_string(),
        port: 5432,
        dbname: "dmail".to_string(),
        user: "postgres".to_string(),
        password: "123456".to_string(),
    };
    
    let client = pg_conn(&config).await.expect("Failed to connect to PostgreSQL");
    let result = init_database_schema(&client).await;
    assert!(result.is_ok(), "Failed to initialize database schema: {:?}", result.err());
    
    // Verify that the users table exists
    let query_result = client.simple_query("SELECT 1 FROM users LIMIT 1").await;
    assert!(query_result.is_ok() || query_result.as_ref().err().unwrap().to_string().contains("relation \"users\" does not exist") == false);
}