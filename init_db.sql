-- Database initialization script for PostgreSQL
-- Run this script to set up the dmail database

-- Create database (run this as superuser)
-- CREATE DATABASE dmail;

-- Connect to the dmail database and run the following:

-- Create users table
DROP TABLE IF EXISTS users CASCADE;

CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    age INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT NULL,
    deleted_at TIMESTAMP DEFAULT NULL
);

-- Create indexes
CREATE INDEX idx_users_deleted_at_age_name_id ON users (deleted_at, age, name, id);

-- Insert some sample data for testing (optional)
INSERT INTO users (name, age) VALUES 
    ('Alice', 25),
    ('<PERSON>', 30),
    ('<PERSON>', 35);

-- Verify the setup
SELECT 'Database setup completed successfully' as status;
SELECT COUNT(*) as user_count FROM users;