# Implementation Plan

- [x] 1. Update project dependencies and configuration

  - Update Cargo.toml to remove SQLite dependencies and add PostgreSQL packages
  - Add tokio-postgres = "0.7.13", tokio-pg-mapper = "0.2.0", tokio-pg-mapper-derive = "0.2.0"
  - Remove rusqlite, tokio-rusqlite-folk, deadpool-sqlite, and rusqlite-from-row dependencies
  - Update .env configuration to include PostgreSQL connection parameters
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5_

- [x] 2. Create PostgreSQL database schema and migration scripts

  - Create PostgreSQL database schema file with users table definition
  - Convert SQLite schema to PostgreSQL syntax (SERIAL PRIMARY KEY, VARCHAR, TIMESTAMP types)
  - Create database indexes equivalent to current SQLite indexes
  - Write SQL migration script to set up initial PostgreSQL database structure
  - _Requirements: 3.1, 3.2, 3.3, 3.4_

- [x] 3. Update database connection module
- [x] 3.1 Create PostgreSQL configuration structure

  - Define PostgresConfig struct with host, port, dbname, user, password fields
  - Implement configuration loading from environment variables
  - Add validation for PostgreSQL connection parameters
  - _Requirements: 4.3_

- [x] 3.2 Implement PostgreSQL connection function

  - Replace db_conn function with pg_conn function using tokio-postgres
  - Implement connection establishment with proper error handling
  - Add connection health check and initialization logic
  - Remove SQLite-specific PRAGMA statements and connection setup
  - _Requirements: 4.1, 4.2_

- [x] 4. Update application state and main entry point
- [x] 4.1 Modify AppState structure

  - Replace tokio_rusqlite_folk::Connection with Arc<tokio_postgres::Client>
  - Update AppState initialization in main.rs
  - Modify serve_app function to use PostgreSQL connection
  - _Requirements: 4.4_

- [x] 4.2 Update environment variable handling

  - Change from dmail_db SQLite file path to PostgreSQL connection parameters
  - Update .env file reading to support PostgreSQL configuration
  - Add proper error handling for missing PostgreSQL environment variables
  - _Requirements: 4.3_

- [x] 5. Update data models and derive macros
- [x] 5.1 Replace rusqlite-from-row with tokio-pg-mapper

  - Update imports from rusqlite_from_row::FromRow to tokio_pg_mapper::FromTokioPostgresRow
  - Replace #[derive(FromRow)] with #[derive(PostgresMapper)] on Table struct
  - Update UserPayload struct to use PostgresMapper derive macro
  - _Requirements: 7.1_

- [x] 5.2 Update timestamp and data type handling

  - Ensure time::OffsetDateTime compatibility with PostgreSQL TIMESTAMP
  - Update serialization/deserialization for PostgreSQL-specific data types
  - Test data model mapping with PostgreSQL row types
  - _Requirements: 7.2, 7.3_

- [x] 6. Update error handling system
- [x] 6.1 Replace SQLite error types with PostgreSQL error types

  - Update HttpResError enum to handle tokio_postgres::Error instead of rusqlite::Error
  - Add tokio_pg_mapper::Error handling to error enum
  - Remove TokioRusqlite error variant and add Postgres error variant
  - _Requirements: 6.1_

- [x] 6.2 Update error response mapping

  - Ensure PostgreSQL errors map to appropriate HTTP status codes
  - Maintain existing error response format for API compatibility
  - Add meaningful error messages for PostgreSQL-specific error conditions
  - _Requirements: 6.2, 6.3, 6.4_

- [x] 7. Convert database query operations
- [x] 7.1 Update user query endpoint (GET /user/q)

  - Replace SQLite parameter syntax (:name, :age) with PostgreSQL syntax ($1, $2)
  - Convert synchronous conn.call() pattern to asynchronous tokio-postgres queries
  - Update query_map usage to use tokio-pg-mapper for result mapping
  - Implement proper parameter binding for PostgreSQL prepared statements
  - _Requirements: 5.1, 5.5, 8.1, 8.4_

- [x] 7.2 Update user creation endpoint (POST /user/c)

  - Convert INSERT query to use PostgreSQL RETURNING syntax
  - Update parameter binding from named_params to positional parameters
  - Modify result mapping to use PostgresMapper instead of FromRow
  - Ensure created user response maintains same JSON structure
  - _Requirements: 5.2, 5.5, 8.2_

- [x] 7.3 Update pagination logic

  - Convert SQLite LIMIT/OFFSET syntax to PostgreSQL equivalent
  - Update cursor-based pagination (id > :id LIMIT :ps) to use PostgreSQL parameters
  - Ensure pagination parameters are properly bound as PostgreSQL types
  - _Requirements: 5.1, 8.3_

- [x] 7.4 Update filtering and WHERE clause construction

  - Convert dynamic WHERE clause building to use PostgreSQL parameter syntax
  - Update age and name filtering to use $1, $2 style parameters
  - Ensure proper parameter type conversion for PostgreSQL queries
  - _Requirements: 5.1, 8.4_

- [x] 8. Create comprehensive test suite
- [x] 8.1 Write unit tests for database connection

  - Test PostgreSQL connection establishment with valid and invalid parameters
  - Test connection error handling and retry logic
  - Verify connection pool behavior and resource cleanup
  - _Requirements: 4.1, 4.2_

- [x] 8.2 Write unit tests for data model mapping

  - Test PostgresMapper derive macro functionality with various data types
  - Test timestamp conversion between PostgreSQL and Rust types
  - Verify serialization/deserialization compatibility
  - _Requirements: 7.1, 7.2, 7.3_

- [x] 8.3 Write integration tests for API endpoints

  - Test user query endpoint with various filter combinations
  - Test user creation endpoint with valid and invalid data
  - Verify pagination works correctly with PostgreSQL backend
  - Test error responses maintain expected format and status codes
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5_

- [x] 9. Performance testing and optimization
- [x] 9.1 Implement connection pooling optimization

  - Configure appropriate connection pool size for expected load
  - Add connection health checks and automatic reconnection logic
  - Monitor connection usage and implement proper cleanup
  - _Requirements: 4.1, 4.2_

- [x] 9.2 Optimize query performance

  - Use prepared statements for frequently executed queries
  - Verify PostgreSQL indexes are properly utilized
  - Compare query performance between SQLite and PostgreSQL implementations
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

- [x] 10. Final integration and validation
- [x] 10.1 End-to-end API testing

  - Run complete test suite against PostgreSQL backend
  - Validate all API endpoints return expected responses
  - Test concurrent access scenarios and connection handling
  - Verify data integrity and consistency
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5_

- [x] 10.2 Documentation and cleanup
  - Update code comments to reflect PostgreSQL usage
  - Remove any remaining SQLite-related code or comments
  - Update README or documentation with PostgreSQL setup instructions
  - Verify all environment variables and configuration are properly documented
  - _Requirements: 1.1, 1.2, 1.3, 1.4_
