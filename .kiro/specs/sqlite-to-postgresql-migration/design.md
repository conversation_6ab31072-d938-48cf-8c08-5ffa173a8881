# Design Document

## Overview

This design document outlines the migration strategy from SQLite to PostgreSQL for the Rust web application. The migration will replace the current SQLite-based database layer with PostgreSQL using `tokio-postgres`, `tokio-pg-mapper`, and `tokio-pg-mapper-derive`. The design ensures minimal disruption to existing API functionality while leveraging PostgreSQL's superior concurrent access and advanced features.

## Architecture

### Current Architecture
- **Database**: SQLite with `rusqlite` and `tokio-rusqlite-folk`
- **Connection Management**: `tokio_rusqlite_folk::Connection` with direct file-based connection
- **Row Mapping**: `rusqlite-from-row` derive macros
- **Error Handling**: `rusqlite::Error` wrapped in custom `HttpResError`

### Target Architecture
- **Database**: PostgreSQL with `tokio-postgres`
- **Connection Management**: `tokio_postgres::Client` with connection pooling
- **Row Mapping**: `tokio-pg-mapper` with `PostgresMapper` derive macro
- **Error Handling**: `tokio_postgres::Error` wrapped in updated `HttpResError`

### Connection Pool Strategy
Since the current implementation uses a single connection approach with `tokio_rusqlite_folk::Connection`, we will implement a simple connection pool using `Arc<Mutex<tokio_postgres::Client>>` or consider using `deadpool-postgres` for more advanced pooling if needed.

## Components and Interfaces

### 1. Database Configuration Module (`svr/db.rs`)

**Current Interface:**
```rust
pub async fn db_conn(db_url: &str) -> Connection
```

**New Interface:**
```rust
pub async fn pg_conn(config: &PostgresConfig) -> Result<Client, tokio_postgres::Error>
pub struct PostgresConfig {
    pub host: String,
    pub port: u16,
    pub dbname: String,
    pub user: String,
    pub password: String,
}
```

**Key Changes:**
- Replace SQLite connection with PostgreSQL client
- Add configuration struct for PostgreSQL connection parameters
- Remove SQLite-specific PRAGMA statements
- Add PostgreSQL connection initialization and health checks

### 2. Application State (`main.rs`)

**Current Structure:**
```rust
pub struct AppState {
    pub dmail_db: Connection,
}
```

**New Structure:**
```rust
pub struct AppState {
    pub dmail_db: Arc<Client>,
}
```

**Key Changes:**
- Replace `tokio_rusqlite_folk::Connection` with `Arc<tokio_postgres::Client>`
- Update initialization logic to use PostgreSQL connection
- Modify environment variable reading for PostgreSQL parameters

### 3. Data Models (`biz/mod.rs`)

**Current Traits:**
```rust
use rusqlite_from_row::FromRow;
#[derive(FromRow)]
pub struct Table<T> { ... }
```

**New Traits:**
```rust
use tokio_pg_mapper::FromTokioPostgresRow;
use tokio_pg_mapper_derive::PostgresMapper;
#[derive(PostgresMapper)]
pub struct Table<T> { ... }
```

**Key Changes:**
- Replace `FromRow` with `PostgresMapper` derive macro
- Update timestamp handling for PostgreSQL `TIMESTAMP` types
- Ensure compatibility with PostgreSQL data types

### 4. Query Operations (`api/dmail/user.rs`)

**Current Pattern:**
```rust
state.dmail_db.call(move |conn| {
    conn.prepare_cached(&sql)?.query_map(params, Table::<UserPayload>::try_from_row)
}).await?
```

**New Pattern:**
```rust
let stmt = state.dmail_db.prepare(&sql).await?;
let rows = state.dmail_db.query(&stmt, &params).await?;
let results: Vec<Table<UserPayload>> = rows.iter()
    .map(|row| Table::<UserPayload>::from_row(row))
    .collect::<Result<Vec<_>, _>>()?;
```

**Key Changes:**
- Replace SQLite parameter syntax (`:name`) with PostgreSQL syntax (`$1`, `$2`)
- Update query execution pattern from synchronous to asynchronous
- Modify result mapping to use `tokio-pg-mapper`

## Data Models

### PostgreSQL Schema Migration

**Current SQLite Schema:**
```sql
CREATE TABLE user (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL COLLATE NOCASE,
    age INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT NULL,
    deleted_at DATETIME DEFAULT NULL
);
CREATE INDEX user_deleted_at_age_name_id_1747242058824 ON user ('deleted_at','age','name','id');
```

**New PostgreSQL Schema:**
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    age INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT NULL,
    deleted_at TIMESTAMP DEFAULT NULL
);
CREATE INDEX idx_users_deleted_at_age_name_id ON users (deleted_at, age, name, id);
```

**Key Differences:**
- `INTEGER PRIMARY KEY AUTOINCREMENT` → `SERIAL PRIMARY KEY`
- `TEXT` → `VARCHAR(255)`
- `DATETIME` → `TIMESTAMP`
- Case-insensitive collation handled at application level if needed
- Index naming follows PostgreSQL conventions

### Data Type Mappings

| SQLite Type | PostgreSQL Type | Rust Type |
|-------------|-----------------|-----------|
| INTEGER | INTEGER | i32 |
| TEXT | VARCHAR(255) | String |
| DATETIME | TIMESTAMP | time::OffsetDateTime |
| BLOB | BYTEA | Vec<u8> |

## Error Handling

### Current Error Types
```rust
#[derive(Error, Debug)]
pub enum HttpResError {
    #[error("A database operation failed: {0:?}")]
    Sqlite(#[from] rusqlite::Error),
    
    #[error("Failed to connect to the database service")]
    TokioRusqlite(#[from] tokio_rusqlite_folk::Error),
}
```

### New Error Types
```rust
#[derive(Error, Debug)]
pub enum HttpResError {
    #[error("A database operation failed: {0:?}")]
    Postgres(#[from] tokio_postgres::Error),
    
    #[error("Failed to map database row: {0:?}")]
    PostgresMapper(#[from] tokio_pg_mapper::Error),
}
```

**Error Mapping Strategy:**
- Map PostgreSQL constraint violations to appropriate HTTP status codes
- Handle connection errors with retry logic where appropriate
- Maintain existing error response format for API compatibility

## Testing Strategy

### Unit Tests
- Test database connection establishment
- Test query parameter binding and execution
- Test row mapping with various data types
- Test error handling for different PostgreSQL error conditions

### Integration Tests
- Test complete API endpoints with PostgreSQL backend
- Test pagination and filtering functionality
- Test transaction handling
- Test concurrent access scenarios

### Migration Testing
- Create test data in SQLite format
- Verify equivalent results from PostgreSQL queries
- Test performance comparison between SQLite and PostgreSQL
- Validate data integrity after migration

## Performance Considerations

### Connection Management
- Implement connection pooling to handle concurrent requests efficiently
- Configure appropriate pool size based on expected load
- Add connection health checks and automatic reconnection

### Query Optimization
- Leverage PostgreSQL's query planner for complex queries
- Use prepared statements for frequently executed queries
- Implement proper indexing strategy for common query patterns

### Memory Usage
- Monitor memory usage with connection pooling
- Optimize row mapping to minimize allocations
- Consider streaming for large result sets

## Security Considerations

### Connection Security
- Use SSL/TLS for database connections in production
- Store database credentials securely using environment variables
- Implement connection timeout and retry policies

### SQL Injection Prevention
- Use parameterized queries exclusively
- Validate input parameters before query execution
- Implement proper escaping for dynamic query construction

### Access Control
- Configure PostgreSQL user permissions appropriately
- Limit database user privileges to minimum required
- Implement connection string validation

## Migration Strategy

### Phase 1: Dependency and Configuration Updates
1. Update Cargo.toml with new dependencies
2. Update environment configuration for PostgreSQL
3. Create PostgreSQL database and schema

### Phase 2: Core Infrastructure Migration
1. Update database connection module
2. Modify application state structure
3. Update error handling system

### Phase 3: Data Model Migration
1. Update data models with PostgresMapper
2. Modify timestamp and data type handling
3. Test model serialization/deserialization

### Phase 4: Query Migration
1. Convert SQLite queries to PostgreSQL syntax
2. Update parameter binding
3. Modify result mapping logic

### Phase 5: Testing and Validation
1. Run comprehensive test suite
2. Validate API response compatibility
3. Performance testing and optimization

## Rollback Strategy

### Preparation
- Maintain SQLite code in feature branches
- Create database backup procedures
- Document rollback steps

### Rollback Triggers
- Critical performance degradation
- Data integrity issues
- Unresolvable compatibility problems

### Rollback Process
1. Switch back to SQLite dependencies
2. Restore SQLite database connection
3. Revert query syntax changes
4. Validate system functionality