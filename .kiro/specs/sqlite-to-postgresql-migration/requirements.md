# Requirements Document

## Introduction

This feature involves migrating the existing Rust web application from SQLite database to PostgreSQL database. The current application uses `rusqlite`, `tokio-rusqlite-folk`, and `deadpool-sqlite` for database operations. The migration will replace these with `tokio-postgres`, `tokio-pg-mapper`, and `tokio-pg-mapper-derive` packages to connect to a local PostgreSQL instance running on port 5432. The migration should maintain all existing functionality while leveraging PostgreSQL's advanced features and better concurrent access capabilities.

## Requirements

### Requirement 1

**User Story:** As a developer, I want to replace SQLite with PostgreSQL database connectivity, so that the application can benefit from PostgreSQL's advanced features and better concurrent access.

#### Acceptance Criteria

1. WHEN the application starts THEN it SHALL connect to a local PostgreSQL database on port 5432
2. WHEN database operations are performed THEN the system SHALL use tokio-postgres instead of rusqlite
3. WHEN mapping database results THEN the system SHALL use tokio-pg-mapper and tokio-pg-mapper-derive
4. WHEN the application configuration is loaded THEN it SHALL read PostgreSQL connection parameters from environment variables

### Requirement 2

**User Story:** As a developer, I want to update the Cargo.toml dependencies, so that the project uses PostgreSQL-specific packages instead of SQLite packages.

#### Acceptance Criteria

1. WHEN the dependencies are updated THEN the system SHALL remove rusqlite, tokio-rusqlite-folk, deadpool-sqlite, and rusqlite-from-row packages
2. WHEN the dependencies are updated THEN the system SHALL add tokio-postgres version 0.7.13
3. WHEN the dependencies are updated THEN the system SHALL add tokio-pg-mapper version 0.2.0
4. WHEN the dependencies are updated THEN the system SHALL add tokio-pg-mapper-derive version 0.2.0
5. WHEN the project builds THEN it SHALL compile successfully with the new PostgreSQL dependencies

### Requirement 3

**User Story:** As a developer, I want to migrate the database schema from SQLite to PostgreSQL, so that the data structure is compatible with PostgreSQL syntax and features.

#### Acceptance Criteria

1. WHEN the PostgreSQL schema is created THEN it SHALL include a users table with equivalent structure to the current SQLite user table
2. WHEN the schema is applied THEN the users table SHALL have columns: id (SERIAL PRIMARY KEY), name (VARCHAR NOT NULL), age (INTEGER NOT NULL), created_at (TIMESTAMP DEFAULT CURRENT_TIMESTAMP), updated_at (TIMESTAMP), deleted_at (TIMESTAMP)
3. WHEN indexes are created THEN they SHALL be equivalent to the current SQLite indexes but using PostgreSQL syntax
4. WHEN the schema is initialized THEN it SHALL use PostgreSQL-specific data types and constraints

### Requirement 4

**User Story:** As a developer, I want to update the database connection and configuration code, so that it connects to PostgreSQL instead of SQLite.

#### Acceptance Criteria

1. WHEN the database connection is established THEN it SHALL use tokio-postgres Client instead of rusqlite Connection
2. WHEN the connection pool is configured THEN it SHALL use a PostgreSQL connection pool instead of SQLite pool
3. WHEN database configuration is loaded THEN it SHALL read PostgreSQL connection string from environment variables
4. WHEN the application state is initialized THEN it SHALL contain PostgreSQL connection objects instead of SQLite objects

### Requirement 5

**User Story:** As a developer, I want to update all database query operations, so that they use PostgreSQL syntax and tokio-pg-mapper instead of rusqlite.

#### Acceptance Criteria

1. WHEN SELECT queries are executed THEN they SHALL use PostgreSQL syntax with $1, $2 parameter placeholders instead of SQLite :name syntax
2. WHEN INSERT operations are performed THEN they SHALL use PostgreSQL RETURNING clause syntax
3. WHEN the query results are mapped THEN they SHALL use tokio-pg-mapper derive macros instead of rusqlite FromRow
4. WHEN database transactions are used THEN they SHALL use PostgreSQL transaction syntax
5. WHEN prepared statements are used THEN they SHALL be compatible with tokio-postgres prepare methods

### Requirement 6

**User Story:** As a developer, I want to update the error handling system, so that it properly handles PostgreSQL-specific errors instead of SQLite errors.

#### Acceptance Criteria

1. WHEN database errors occur THEN the system SHALL handle tokio-postgres::Error instead of rusqlite::Error
2. WHEN connection errors happen THEN they SHALL be properly mapped to HTTP error responses
3. WHEN query errors occur THEN they SHALL provide meaningful error messages for PostgreSQL-specific issues
4. WHEN the error handling is updated THEN it SHALL maintain the same HTTP status code responses for equivalent error conditions

### Requirement 7

**User Story:** As a developer, I want to update the data models and serialization, so that they work correctly with PostgreSQL data types and tokio-pg-mapper.

#### Acceptance Criteria

1. WHEN data models are defined THEN they SHALL use tokio-pg-mapper derive macros instead of rusqlite-from-row
2. WHEN timestamp fields are handled THEN they SHALL be compatible with PostgreSQL TIMESTAMP types
3. WHEN data serialization occurs THEN it SHALL properly handle PostgreSQL-specific data types
4. WHEN the models are used in queries THEN they SHALL work seamlessly with tokio-pg-mapper mapping functionality

### Requirement 8

**User Story:** As a developer, I want to ensure all existing API endpoints continue to work, so that the migration is transparent to API consumers.

#### Acceptance Criteria

1. WHEN the user query endpoint (/user/q) is called THEN it SHALL return the same response format as before
2. WHEN the user creation endpoint (/user/c) is called THEN it SHALL create users in PostgreSQL and return the same response format
3. WHEN pagination is used THEN it SHALL work correctly with PostgreSQL LIMIT and OFFSET syntax
4. WHEN filtering by age and name is applied THEN it SHALL produce equivalent results using PostgreSQL WHERE clauses
5. WHEN the API responses are returned THEN they SHALL maintain the same JSON structure and data types