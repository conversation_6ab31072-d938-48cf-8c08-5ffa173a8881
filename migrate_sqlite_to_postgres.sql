-- Migration script from SQLite to PostgreSQL
-- This script helps migrate existing SQLite data to PostgreSQL

-- Step 1: Create the PostgreSQL table structure
DROP TABLE IF EXISTS users CASCADE;

CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    name <PERSON><PERSON>HA<PERSON>(255) NOT NULL,
    age INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT NULL,
    deleted_at TIMESTAMP DEFAULT NULL
);

-- Step 2: Create indexes
CREATE INDEX idx_users_deleted_at_age_name_id ON users (deleted_at, age, name, id);

-- Step 3: Data migration notes
-- To migrate data from SQLite to PostgreSQL:
-- 1. Export SQLite data: sqlite3 dmail.db ".dump user" > sqlite_data.sql
-- 2. Convert SQLite INSERT statements to PostgreSQL format
-- 3. Handle data type conversions:
--    - SQLite DATETIME -> PostgreSQL TIMESTAMP
--    - SQLite INTEGER PRIMARY KEY AUTOINCREMENT -> PostgreSQL SERIAL
-- 4. Import converted data into PostgreSQL

-- Example of converted INSERT statement:
-- SQLite: INSERT INTO user (id, name, age, created_at) VALUES (1, 'John', 25, '2024-01-01 10:00:00');
-- PostgreSQL: INSERT INTO users (name, age, created_at) VALUES ('John', 25, '2024-01-01 10:00:00'::timestamp);
-- Note: Don't insert id values, let SERIAL handle auto-increment

-- Step 4: Verify migration
-- SELECT COUNT(*) FROM users;
-- SELECT * FROM users LIMIT 5;