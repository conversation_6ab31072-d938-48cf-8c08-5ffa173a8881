
mod svr;
mod api;
mod biz;
mod server;

use api::{dmail::build_dmail_routes};
use axum::Router;
use dotenvy::from_filename;
use svr::db::{pg_conn, init_database_schema, PostgresConfig};
use tokio_postgres::Client;
use std::{env, sync::Arc};
// use server;

#[derive(Clone)]
pub struct AppState {
    pub dmail_db: Arc<Client>,
}




// tokio::runtime::Builder::new_multi_thread()
    // .worker_threads(8)      // 工作线程数（默认=CPU核心数）
    // .max_blocking_threads(256) // 这里的threads 是 异步任务数。为什么不设置得太低？如果你设置的 max_blocking_threads 太低，就相当于你只雇佣了很少的临时帮手。当同时出现多个耗时的任务时，它们就需要长时间排队等待，导致你的应用程序在处理这些操作时可能会“卡住”或者响应缓慢。
    // .thread_stack_size(1 * 1024 * 1024) //
    // .enable_all()
    // .build()
    // .unwrap()
    // .block_on(async {

    // });


    //       .layer(
    //   TraceLayer::new_for_http()
    //     .make_span_with(|request: &Request<Body>| {
    //       let headers = request.headers();
    //       let user_agent = headers
    //         .get("Lrclib-Client")
    //         .and_then(|value| value.to_str().ok())
    //         .or_else(|| headers.get("X-User-Agent").and_then(|value| value.to_str().ok()))
    //         .or_else(|| headers.get(header::USER_AGENT).and_then(|value| value.to_str().ok()))
    //         .unwrap_or("");
    //       let method = request.method().to_string();
    //       let uri = request.uri().to_string();

    //       tracing::debug_span!("request", method, uri, user_agent)
    //     })
    //     .on_response(|response: &Response, latency: Duration, _span: &Span| {
    //       let status_code = response.status().as_u16();
    //       let latency = latency.as_millis();

    //       if latency > 500 {
    //         tracing::info!(
    //           message = "finished processing request",
    //           slow = true,
    //           latency = latency,
    //           status_code = status_code,
    //         )
    //       } else {
    //         tracing::debug!(
    //           message = "finished processing request",
    //           latency = latency,
    //           status_code = status_code,
    //         )
    //       }
    //     })
    //     .on_failure(trace::DefaultOnFailure::new().level(tracing::Level::ERROR))
    //     .on_request(move |_request: &Request<Body>, _span: &Span| {
    //       state_for_logging.request_counter.fetch_add(1, Ordering::Relaxed);
    //     })
    // );




// #[tokio::main(flavor = "multi_thread",worker_threads = 8)]
// async fn main() {
//     // starttime
//     from_filename(".env").ok();
//     let dmail_db = db_conn(&env::var("dmail_db").unwrap()).await;




//     // fmt().with_max_level(Level::INFO).init();

//     // runtime
//     let app = Router::new()
//         .merge(build_dmail_routes())
//         .with_state(AppState {dmail_db});






//     // Run the server
//     let addr = SocketAddr::from(([0, 0, 0, 0], env::var("port").unwrap().parse::<u16>().unwrap()));
//     let listener = tokio::net::TcpListener::bind(addr).await.unwrap();
//     axum::serve(listener, app).await.unwrap();
// }


// #[tokio::main(flavor = "multi_thread",worker_threads = 8)]
fn main() {
    from_filename(".env").ok();
    server::start_tokio(serve_app)
}

// #[tokio::main(flavor = "multi_thread",worker_threads = 8)]
async fn serve_app() {
    // Load PostgreSQL configuration from environment variables
    let pg_config = PostgresConfig::from_env()
        .expect("Failed to load PostgreSQL configuration from environment variables");

    // Create PostgreSQL connection
    let client = pg_conn(&pg_config).await
        .expect("Failed to connect to PostgreSQL database");

    // Initialize database schema
    init_database_schema(&client).await
        .expect("Failed to initialize database schema");

    let dmail_db = Arc::new(client);





    // let app = Router::new()
    //     .route("/fortunes", get(fortunes))
    //     .route("/db", get(db))
    //     .route("/queries", get(queries))
    //     .route("/updates", get(updates))
    //     .with_state(conn);


    let app = Router::new().merge(build_dmail_routes()).with_state(AppState {dmail_db});


    // Run the server
    // let addr = SocketAddr::from(([0, 0, 0, 0], env::var("port").unwrap().parse::<u16>().unwrap()));
    // let listener = tokio::net::TcpListener::bind(addr).await.unwrap();
    // axum::serve(listener, app).await.unwrap();

    server::serve(app, Some(env::var("port").unwrap().parse::<u16>().unwrap())).await
}