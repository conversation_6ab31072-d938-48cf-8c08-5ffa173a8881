# PostgreSQL Database Setup

This document describes how to set up PostgreSQL for the Dmail application after migrating from SQLite.

## Prerequisites

1. Install PostgreSQL on your system
2. Ensure PostgreSQL service is running on port 5432
3. Have a PostgreSQL user with database creation privileges

## Database Setup Steps

### 1. Create Database

Connect to PostgreSQL as a superuser and create the database:

```sql
CREATE DATABASE dmail;
CREATE USER postgres WITH PASSWORD '123456';
GRANT ALL PRIVILEGES ON DATABASE dmail TO postgres;
```

### 2. Initialize Schema

Run the initialization script:

```bash
psql -h localhost -p 5432 -U postgres -d dmail -f init_db.sql
```

Or manually execute the schema:

```bash
psql -h localhost -p 5432 -U postgres -d dmail -f schema.sql
```

### 3. Verify Setup

Connect to the database and verify:

```bash
psql -h localhost -p 5432 -U postgres -d dmail
```

```sql
\dt  -- List tables
\d users  -- Describe users table
SELECT COUNT(*) FROM users;  -- Check if sample data exists
```

## Environment Configuration

Ensure your `.env` file contains the correct PostgreSQL connection parameters:

```env
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=dmail
POSTGRES_USER=postgres
POSTGRES_PASSWORD=123456
```

## Migration from SQLite

If you have existing SQLite data to migrate:

1. Export SQLite data:
   ```bash
   sqlite3 dmail.db ".dump user" > sqlite_data.sql
   ```

2. Convert and import data using the migration script:
   ```bash
   # Edit migrate_sqlite_to_postgres.sql with your converted data
   psql -h localhost -p 5432 -U postgres -d dmail -f migrate_sqlite_to_postgres.sql
   ```

## Schema Differences

| Aspect | SQLite | PostgreSQL |
|--------|--------|------------|
| Table Name | `user` | `users` |
| Primary Key | `INTEGER PRIMARY KEY AUTOINCREMENT` | `SERIAL PRIMARY KEY` |
| Text Fields | `TEXT` | `VARCHAR(255)` |
| Timestamps | `DATETIME` | `TIMESTAMP` |
| Case Sensitivity | `COLLATE NOCASE` | Handled at application level |

## Troubleshooting

### Connection Issues
- Verify PostgreSQL is running: `pg_ctl status`
- Check port availability: `netstat -an | grep 5432`
- Verify user permissions: `psql -U postgres -l`

### Schema Issues
- Drop and recreate if needed: `DROP TABLE users CASCADE;`
- Check table exists: `\dt` in psql
- Verify indexes: `\di` in psql